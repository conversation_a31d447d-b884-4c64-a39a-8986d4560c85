package binance

// NewBroker creates a new broker instance based on the configuration
func NewBroker(config *Config) Broker {
	if config == nil {
		config = &Config{TestMode: true} // Default to test mode for safety
	}

	if config.TestMode {
		return NewTestBroker()
	}

	return NewClient(config)
}

// NewMarketDataBroker creates a new market data broker (always uses real data)
func NewMarketDataBroker() MarketDataBroker {
	return NewClient(&Config{TestMode: false})
}

// NewTradingBroker creates a new trading broker (respects test mode)
func NewTradingBroker(config *Config) TradingBroker {
	if config == nil || config.TestMode {
		return NewTestBroker()
	}

	return NewClient(config)
}
