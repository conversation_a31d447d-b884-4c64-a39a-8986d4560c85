package binance

import (
	"context"
	"fmt"
	"log"
	"math/rand"
	"strconv"
	"sync"
	"time"

	"github.com/adshao/go-binance/v2"
)

// TestBroker is a mock implementation of the Broker interface for testing
type TestBroker struct {
	sync.RWMutex
	testMode           bool
	client            *binance.Client // Client for public API calls
	orders            map[int64]*binance.Order
	pendingOrders     map[int64]*binance.Order // Track pending orders
	orderHistory      map[int64][]*binance.Order // Order history by order ID
	balances          map[string]float64
	klineSubscribers  map[string]map[chan struct{}]WsKlineHandler
	orderBookHandlers map[string]map[chan struct{}]func(*OrderBook) // Order book update handlers
	orderBooks       map[string]*OrderBook // Cache of order books by symbol
	orderIDGen        int64
	lastCandles      map[string][]*binance.Kline // Track recent candles per symbol
	candleCache      map[string]map[string][]*binance.Kline // Cache for kline data [symbol][interval][]kline
}

// NewTestBroker creates a new test broker instance
func NewTestBroker() *TestBroker {
	// Create a client without API keys for public endpoints
	client := binance.NewClient("", "")
	
	return &TestBroker{
		testMode:          true,
		client:            client,
		orders:            make(map[int64]*binance.Order),
		pendingOrders:     make(map[int64]*binance.Order),
		orderHistory:      make(map[int64][]*binance.Order),
		balances:          make(map[string]float64),
		klineSubscribers:  make(map[string]map[chan struct{}]WsKlineHandler),
		orderBookHandlers: make(map[string]map[chan struct{}]func(*OrderBook)),
		orderBooks:        make(map[string]*OrderBook),
		orderIDGen:        1000,
		lastCandles:      make(map[string][]*binance.Kline),
		candleCache:      make(map[string]map[string][]*binance.Kline),
	}
}

// SetTestMode enables or disables test mode
func (b *TestBroker) SetTestMode(enabled bool) {
	b.Lock()
	defer b.Unlock()
	b.testMode = enabled
}

// IsTestMode returns true if test mode is enabled
func (b *TestBroker) IsTestMode() bool {
	b.RLock()
	defer b.RUnlock()
	return b.testMode
}

// Public methods (implement MarketDataBroker interface)

func (b *TestBroker) Ping(ctx context.Context) error {
	return nil
}

func (b *TestBroker) GetServerTime(ctx context.Context) (int64, error) {
	return time.Now().Unix() * 1000, nil
}

func (b *TestBroker) GetExchangeInfo(ctx context.Context) (*binance.ExchangeInfo, error) {
	return &binance.ExchangeInfo{
		Timezone:   "UTC",
		ServerTime: time.Now().Unix() * 1000,
	}, nil
}

func (b *TestBroker) GetTickerPrice(ctx context.Context, symbol string) (string, error) {
	// Get the latest price from Binance public API
	prices, err := b.client.NewListPricesService().Symbol(symbol).Do(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get ticker price: %w", err)
	}

	if len(prices) == 0 {
		return "", fmt.Errorf("no price data available for symbol %s", symbol)
	}

	return prices[0].Price, nil
}

// GetOrderBook gets the current order book for a symbol
func (b *TestBroker) GetOrderBook(ctx context.Context, symbol string, limit int) (*OrderBook, error) {
	// Try to get from cache first
	b.RLock()
	cachedBook, ok := b.orderBooks[symbol]
	b.RUnlock()

	if ok && time.Since(time.Unix(0, cachedBook.Timestamp*int64(time.Millisecond))) < 5*time.Second {
		return cachedBook, nil
	}

	// Fetch from Binance API if not in cache or stale
	binanceBook, err := b.client.NewDepthService().Symbol(symbol).Limit(limit).Do(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get order book: %w", err)
	}

	// Convert Binance order book to our format
	book := &OrderBook{
		LastUpdateID: binanceBook.LastUpdateID,
		Symbol:       symbol,
		Timestamp:    time.Now().UnixNano() / int64(time.Millisecond),
		Bids:         make([]OrderBookEntry, 0, len(binanceBook.Bids)),
		Asks:         make([]OrderBookEntry, 0, len(binanceBook.Asks)),
	}

	// Process bids (already sorted from highest to lowest)
	for _, bid := range binanceBook.Bids {
		book.Bids = append(book.Bids, OrderBookEntry{
			Price:    bid.Price,
			Quantity: bid.Quantity,
		})
	}

	// Process asks (already sorted from lowest to highest)
	for _, ask := range binanceBook.Asks {
		book.Asks = append(book.Asks, OrderBookEntry{
			Price:    ask.Price,
			Quantity: ask.Quantity,
		})
	}

	// Update cache
	b.Lock()
	b.orderBooks[symbol] = book
	b.Unlock()

	return book, nil
}

// SubscribeToOrderBook subscribes to order book updates for a symbol
func (b *TestBroker) SubscribeToOrderBook(ctx context.Context, symbol string, handler func(book *OrderBook)) (doneC, stopC chan struct{}, err error) {
	doneC = make(chan struct{})
	stopC = make(chan struct{})

	// Initialize handlers map for this symbol if needed
	b.Lock()
	if _, ok := b.orderBookHandlers[symbol]; !ok {
		b.orderBookHandlers[symbol] = make(map[chan struct{}]func(*OrderBook))
	}

	// Store the handler
	handlerID := doneC
	b.orderBookHandlers[symbol][handlerID] = handler
	b.Unlock()

	// Start a goroutine to fetch order book updates
	go func() {
		ticker := time.NewTicker(1 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				book, err := b.GetOrderBook(ctx, symbol, 20) // Get top 20 levels
				if err != nil {
					log.Printf("Error getting order book: %v", err)
					continue
				}

				b.RLock()
				handlers, ok := b.orderBookHandlers[symbol]
				b.RUnlock()

				if ok {
					for _, h := range handlers {
						h(book)
					}
				}

			case <-stopC:
				b.Lock()
				delete(b.orderBookHandlers[symbol], handlerID)
				b.Unlock()
				close(doneC)
				return
			case <-ctx.Done():
				b.Lock()
				delete(b.orderBookHandlers[symbol], handlerID)
				b.Unlock()
				close(doneC)
				return
			}
		}
	}()

	return doneC, stopC, nil
}

// GetKlines gets kline/candlestick data for a symbol from Binance public API
func (b *TestBroker) GetKlines(ctx context.Context, symbol, interval string, limit int) ([]*binance.Kline, error) {
	// Check cache first
	cacheKey := fmt.Sprintf("%s_%s", symbol, interval)
	if _, ok := b.candleCache[cacheKey]; !ok {
		b.candleCache[cacheKey] = make(map[string][]*binance.Kline)
	}

	// Return cached data if available
	if cached, ok := b.candleCache[cacheKey][interval]; ok && len(cached) > 0 {
		if len(cached) >= limit {
			return cached[:limit], nil
		}
	}

	// Fetch from Binance API
	klines, err := b.client.NewKlinesService().
		Symbol(symbol).
		Interval(interval).
		Limit(limit).
		Do(ctx)

	if err != nil {
		return nil, fmt.Errorf("failed to get klines: %w", err)
	}

	// Cache the result
	b.candleCache[cacheKey][interval] = klines

	// Update last candles for the symbol
	b.Lock()
	b.lastCandles[symbol] = klines
	b.Unlock()

	return klines, nil
}

// Private methods (implement TradingBroker interface)

func (b *TestBroker) GetAccountInfo(ctx context.Context) (*binance.Account, error) {
	b.RLock()
	defer b.RUnlock()

	if !b.testMode {
		return nil, fmt.Errorf("test mode is disabled")
	}

	balances := make([]binance.Balance, 0, len(b.balances))
	for asset, free := range b.balances {
		balances = append(balances, binance.Balance{
			Asset:  asset,
			Free:   fmt.Sprintf("%.8f", free),
			Locked: "0.********",
		})
	}

	return &binance.Account{
		Balances: balances,
	}, nil
}

func (b *TestBroker) GetBalance(ctx context.Context, asset string) (*binance.Balance, error) {
	b.RLock()
	defer b.RUnlock()

	if !b.testMode {
		return nil, fmt.Errorf("test mode is disabled")
	}

	free, exists := b.balances[asset]
	if !exists {
		free = 0.0
	}

	return &binance.Balance{
		Asset:  asset,
		Free:   fmt.Sprintf("%.8f", free),
		Locked: "0.********",
	}, nil
}

func (b *TestBroker) CreateOrder(ctx context.Context, orderReq *OrderRequest) (*OrderResponse, error) {
	b.Lock()

	// Generate a new order ID
	orderID := b.orderIDGen
	b.orderIDGen++

	// Create the order
	now := time.Now().Unix() * 1000
	order := &binance.Order{
		Symbol:           orderReq.Symbol,
		OrderID:          orderID,
		ClientOrderID:    orderReq.NewClientOrderID,
		Price:            orderReq.Price,
		OrigQuantity:     orderReq.Quantity,
		ExecutedQuantity: "0",
		Status:           binance.OrderStatusTypeNew,
		TimeInForce:      binance.TimeInForceType(orderReq.TimeInForce),
		Type:             binance.OrderType(orderReq.Type),
		Side:             binance.SideType(orderReq.Side),
		StopPrice:        orderReq.StopPrice,
		IcebergQuantity:  orderReq.IcebergQuantity,
		Time:             now,
		UpdateTime:       now,
		IsWorking:        true,
	}

	// Add to pending orders
	b.pendingOrders[orderID] = order
	b.Unlock()

	// For market orders, execute immediately at the current price
	if orderReq.Type == OrderTypeMarket {
		// Get current price from recent candles if available
		price := "100.0" // Default price if no market data
		if candles, ok := b.lastCandles[orderReq.Symbol]; ok && len(candles) > 0 {
			price = candles[len(candles)-1].Close
		}

		// Create a kline that will trigger the order execution
		kline := &binance.Kline{
			Open:             price,
			High:             price,
			Low:              price,
			Close:            price,
			Volume:           orderReq.Quantity,
			OpenTime:         now,
			CloseTime:        now + 60000, // 1 minute later
			QuoteAssetVolume: "0",
		}

		// Process the order execution
		b.processOrderExecution(orderReq.Symbol, kline)
	}

	// Convert to response
	b.RLock()
	defer b.RUnlock()
	
	resp := &OrderResponse{
		Symbol:              order.Symbol,
		OrderID:             order.OrderID,
		ClientOrderID:       order.ClientOrderID,
		TransactTime:        now,
		Price:               order.Price,
		OrigQty:             order.OrigQuantity,
		ExecutedQty:         order.ExecutedQuantity,
		Status:              string(order.Status),
		TimeInForce:         string(order.TimeInForce),
		Type:                string(order.Type),
		Side:                string(order.Side),
		Fills:               nil, // Not implemented in test
	}

	return resp, nil
}

func (b *TestBroker) GetOpenOrders(ctx context.Context, symbol string) ([]*binance.Order, error) {
	b.RLock()
	defer b.RUnlock()

	if !b.testMode {
		return nil, fmt.Errorf("test mode is disabled")
	}

	var orders []*binance.Order
	for _, order := range b.orders {
		if order.Symbol == symbol && order.Status == binance.OrderStatusTypeNew {
			orders = append(orders, order)
		}
	}

	return orders, nil
}

func (b *TestBroker) CancelOrder(ctx context.Context, symbol string, orderID int64) error {
	b.Lock()
	defer b.Unlock()

	if !b.testMode {
		return fmt.Errorf("test mode is disabled")
	}

	order, exists := b.orders[orderID]
	if !exists {
		return fmt.Errorf("order not found")
	}

	order.Status = binance.OrderStatusTypeCanceled
	return nil
}

// SetTestBalance sets a test balance for an asset (for testing purposes)
func (b *TestBroker) SetTestBalance(asset string, amount float64) {
	b.Lock()
	defer b.Unlock()
	b.balances[asset] = amount
}

// processOrderExecution checks if any pending orders should be executed based on market data
func (b *TestBroker) processOrderExecution(symbol string, kline *binance.Kline) {
	b.Lock()
	defer b.Unlock()

	// Update last candles for this symbol
	if _, exists := b.lastCandles[symbol]; !exists {
		b.lastCandles[symbol] = make([]*binance.Kline, 0, 100)
	}
	b.lastCandles[symbol] = append(b.lastCandles[symbol], kline)
	if len(b.lastCandles[symbol]) > 100 {
		b.lastCandles[symbol] = b.lastCandles[symbol][1:]
	}

	// Check pending orders for this symbol
	for orderID, order := range b.pendingOrders {
		if order.Symbol != symbol {
			continue
		}

		// Convert kline prices to float for comparison
		low, _ := strconv.ParseFloat(kline.Low, 64)
		high, _ := strconv.ParseFloat(kline.High, 64)
		orderPrice, _ := strconv.ParseFloat(order.Price, 64)

		// Check if order price is within the kline range
		if orderPrice >= low && orderPrice <= high {
			// Order would have been executed
			order.Status = binance.OrderStatusTypeFilled
			order.ExecutedQuantity = order.OrigQuantity
			order.UpdateTime = time.Now().Unix() * 1000

			// Update balances
			baseAsset := symbol[:3] // Simple assumption about asset pairs
			quoteAsset := symbol[3:]


			if order.Side == binance.SideTypeBuy {
				// Decrease quote asset, increase base asset
				b.balances[quoteAsset] -= orderPrice
				b.balances[baseAsset] += 1.0 // Simplified for example
			} else {
				// Decrease base asset, increase quote asset
				b.balances[baseAsset] -= 1.0 // Simplified for example
				b.balances[quoteAsset] += orderPrice
			}

			// Move to orders history
			b.orders[orderID] = order
			b.orderHistory[orderID] = append(b.orderHistory[orderID], order)
			delete(b.pendingOrders, orderID)
		}
	}
}

// SubscribeToMarketData implements the MarketDataBroker interface
func (b *TestBroker) SubscribeToMarketData(ctx context.Context, symbol, interval string, handler WsKlineHandler) (doneC, stopC chan struct{}, err error) {
	doneC = make(chan struct{})
	stopC = make(chan struct{})

	b.Lock()
	if _, ok := b.klineSubscribers[symbol]; !ok {
		b.klineSubscribers[symbol] = make(map[chan struct{}]WsKlineHandler)
	}
	b.klineSubscribers[symbol][doneC] = handler
	b.Unlock()

	// Start a goroutine to simulate market data updates
	go func() {
		ticker := time.NewTicker(1 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				// Generate test data
				event := &WsKline{
					Symbol:     symbol,
					Interval:   interval,
					StartTime:  time.Now().Unix() * 1000,
					CloseTime:  time.Now().Add(1 * time.Minute).Unix() * 1000,
					Open:       "100.0",
					Close:      fmt.Sprintf("%.2f", 100.0 + rand.Float64() * 2.0 - 1.0), // Random price around 100
					High:       "102.0",
					Low:        "98.0",
					Volume:     fmt.Sprintf("%.2f", 1000.0 + rand.Float64() * 500.0), // Random volume
					IsFinal:    false,
				}

				b.RLock()
				handler, ok := b.klineSubscribers[symbol][doneC]
				b.RUnlock()

				if ok && handler != nil {
					handler(event)
				}

			case <-stopC:
				b.Lock()
				delete(b.klineSubscribers[symbol], doneC)
				b.Unlock()
				close(doneC)
				return
			case <-ctx.Done():
				b.Lock()
				delete(b.klineSubscribers[symbol], doneC)
				b.Unlock()
				close(doneC)
				return
			}
		}
	}()

	return doneC, stopC, nil
}
