import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import './style.css'

// Initialize plugins
const app = createApp(App)
const pinia = createPinia()

// Use plugins
app.use(router)
app.use(pinia)

// Global error handler
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue error:', err)
  if (window.showNotification) {
    const message = err instanceof Error ? err.message : 'An unexpected error occurred'
    window.showNotification(message, 'error')
  }
}

// Mount the app when router is ready
router.isReady().then(() => {
  app.mount('#app')
})

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason)
  if (window.showNotification) {
    const message = event.reason?.message || 'An unexpected error occurred'
    window.showNotification(message, 'error')
  }
})

// Import types
import './types/wails'
