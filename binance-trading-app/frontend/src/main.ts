import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createI18n } from 'vue-i18n'
import { createHead } from '@vueuse/head'
import { HeadlessUiPlugin } from '@headlessui/vue'
import * as HeroIcons from '@heroicons/vue/24/outline'
import App from './App.vue'
import router from './router'
import './style.css'

// Initialize plugins
const app = createApp(App)
const pinia = createPinia()
const head = createHead()

// Configure i18n
const i18n = createI18n({
  legacy: false,
  locale: 'en',
  fallbackLocale: 'en',
  messages: {
    en: {},
    ru: {}
  },
  silentTranslationWarn: true
})

// Use plugins
app.use(router)
app.use(pinia)
app.use(i18n)
app.use(head)
app.use(HeadlessUiPlugin)

// Register Hero Icons globally
Object.entries(HeroIcons).forEach(([name, component]) => {
  if (name.startsWith('Icon')) {
    app.component(name, component)
  }
})

// Global error handler
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue error:', err)
  if (window.showNotification) {
    const message = err instanceof Error ? err.message : 'An unexpected error occurred'
    window.showNotification(message, 'error')
  }
}

// Mount the app when router is ready
router.isReady().then(() => {
  app.mount('#app')
})

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason)
  if (window.showNotification) {
    const message = event.reason?.message || 'An unexpected error occurred'
    window.showNotification(message, 'error')
  }
})

// Global type declarations
declare global {
  interface Window {
    // Wails bindings
    ConnectToBinance: (apiKey: string, apiSecret: string) => Promise<any>
    GetStoredKeys: () => Promise<{ apiKey: string; apiSecret: string } | null>
    SetBinanceAPIKeys: (apiKey: string, apiSecret: string) => Promise<void>
    GetAccountInfo: () => Promise<any>
    
    // Notification system
    showNotification: (message: string, type?: 'info' | 'success' | 'warning' | 'error', duration?: number) => void
    
    // Add more methods as needed
  }
}
