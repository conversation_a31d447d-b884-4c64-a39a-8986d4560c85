<template>
  <div class="trading-view">
    <!-- Header -->
    <header class="trading-header">
      <div class="symbol-selector">
        <select 
          v-model="selectedSymbol" 
          class="select"
          :disabled="isLoadingSymbols || !availableSymbols.length"
        >
          <option 
            v-for="symbol in availableSymbols" 
            :key="symbol" 
            :value="symbol"
          >
            {{ symbol }}
          </option>
        </select>
        <div class="price-info">
          <span class="price" :class="getPriceChangeClass(priceChange24h)">
            {{ formatPrice(currentPrice) }}
          </span>
          <span class="change" :class="getPriceChangeClass(priceChange24h)">
            {{ formatPriceChange(priceChange24h, priceChangePercent24h) }}
          </span>
        </div>
      </div>
      
      <div class="header-actions">
        <div v-if="store.isTestMode" class="test-mode-badge">
          <span class="test-mode-dot"></span>
          Test Mode
        </div>
        <button 
          v-tooltip="isDark ? 'Switch to Light Mode' : 'Switch to Dark Mode'"
          class="btn-icon" 
          @click="toggleTheme"
        >
          <IconSun v-if="isDark" class="h-5 w-5" />
          <IconMoon v-else class="h-5 w-5" />
        </button>
        <button 
          v-tooltip="'Disconnect'"
          class="btn-icon text-red-500 hover:bg-red-500/10"
          @click="disconnect"
        >
          <IconPower class="h-5 w-5" />
        </button>
      </div>
    </header>

    <!-- Main Content -->
    <main class="trading-main">
      <!-- Chart Section -->
      <div class="chart-section">
        <TradingChart 
          :symbol="selectedSymbol" 
          :current-price="currentPrice"
          :is-loading="isLoading"
          @price-update="handlePriceUpdate"
        />
      </div>

      <!-- Order Book Section -->
      <div class="order-book-section">
        <OrderBook 
          :symbol="selectedSymbol"
          :is-loading="isLoading"
        />
      </div>

      <!-- Order Form Section -->
      <div class="order-form-section">
        <OrderForm 
          :symbol="selectedSymbol" 
          :current-price="currentPrice"
          :account-info="accountInfo"
          :is-loading="isLoading"
          @order-submitted="handleOrderSubmitted"
        />
      </div>
    </main>


    <!-- Loading Overlay -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p class="loading-text">{{ loadingMessage }}</p>
    </div>
    
    <!-- Error Message -->
    <div v-if="error" class="error-message">
      <IconExclamationCircle class="h-5 w-5" />
      {{ error }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useTradingStore } from '@/stores/trading'
import { formatNumber, formatPrice, formatPercent } from '@/utils/formatters'
import TradingChart from '@/components/TradingChart.vue'
import OrderBook from '@/components/OrderBook.vue'
import OrderForm from '@/components/OrderForm.vue'
import { 
  IconSun, 
  IconMoon, 
  IconPower,
  IconExclamationCircle,
  IconArrowsRightLeft
} from '@heroicons/vue/24/outline'

const router = useRouter()
const store = useTradingStore()

// Theme
const isDark = ref(true)

// Component state
const selectedSymbol = ref('BTCUSDT')
const currentPrice = ref('0')
const priceChange24h = ref(0)
const priceChangePercent24h = ref(0)
const isLoading = ref(true)
const isLoadingSymbols = ref(false)
const accountInfo = ref<Record<string, any> | null>(null)
const availableSymbols = ref<string[]>([])
const error = ref('')
const loadingMessage = ref('Loading market data...')

// Computed
const formattedCurrentPrice = computed(() => {
  return formatPrice(currentPrice.value)
})

const isTestMode = computed(() => store.isTestMode)

// Methods
const toggleTheme = () => {
  isDark.value = !isDark.value
  document.documentElement.classList.toggle('dark', isDark.value)
  store.setTheme(isDark.value ? 'dark' : 'light')
}

const formatPriceChange = (change: number, percent: number) => {
  const sign = change >= 0 ? '+' : ''
  return `${sign}${formatNumber(change, 2)} (${sign}${formatNumber(percent, 2)}%)`
}

const getPriceChangeClass = (change: number) => ({
  'text-green-500': change > 0,
  'text-red-500': change < 0,
  'text-gray-400': change === 0
})

const handlePriceUpdate = (price: string) => {
  currentPrice.value = price
}

const handleOrderSubmitted = async () => {
  // Refresh account info and open orders after order submission
  await Promise.all([
    fetchAccountInfo(),
    fetchOpenOrders()
  ])
}

const disconnect = async () => {
  try {
    isLoading.value = true
    loadingMessage.value = 'Disconnecting...'
    
    // Clear store and reset state
    store.reset()
    
    // Clear API keys from backend
    await window.SetBinanceAPIKeys('', '')
    
    // Redirect to connection page
    router.push('/')
  } catch (err) {
    console.error('Error disconnecting:', err)
    error.value = 'Failed to disconnect. Please try again.'
  } finally {
    isLoading.value = false
  }
}

// Data fetching methods
const fetchAccountInfo = async () => {
  if (store.isTestMode) return
  
  try {
    const account = await window.GetAccountInfo()
    if (account) {
      accountInfo.value = account
      store.setAccountInfo(account)
    }
  } catch (err) {
    console.error('Error fetching account info:', err)
    error.value = 'Failed to load account information'
  }
}

const fetchTickerInfo = async () => {
  if (!selectedSymbol.value) return
  
  try {
    const ticker = await window.GetTicker({ symbol: selectedSymbol.value })
    if (ticker) {
      currentPrice.value = ticker.lastPrice
      priceChange24h.value = parseFloat(ticker.priceChange)
      priceChangePercent24h.value = parseFloat(ticker.priceChangePercent)
      
      // Update store with latest price
      store.updateSymbolPrice(selectedSymbol.value, ticker.lastPrice)
    }
  } catch (err) {
    console.error('Error fetching ticker info:', err)
    error.value = `Failed to load market data for ${selectedSymbol.value}`
  }
}

const fetchAvailableSymbols = async () => {
  try {
    isLoadingSymbols.value = true
    const symbols = await window.GetAvailableSymbols()
    if (symbols && Array.isArray(symbols)) {
      availableSymbols.value = symbols
        .filter(s => s.endsWith('USDT')) // Only show USDT pairs for now
        .sort()
      
      // If selected symbol is not in available symbols, select first one
      if (availableSymbols.value.length > 0 && !availableSymbols.value.includes(selectedSymbol.value)) {
        selectedSymbol.value = availableSymbols.value[0]
      }
    }
  } catch (err) {
    console.error('Error fetching available symbols:', err)
    error.value = 'Failed to load trading pairs'
  } finally {
    isLoadingSymbols.value = false
  }
}

const fetchOpenOrders = async () => {
  if (!selectedSymbol.value) return
  
  try {
    const orders = await window.GetOpenOrders({ symbol: selectedSymbol.value })
    store.setOpenOrders(orders || [])
  } catch (err) {
    console.error('Error fetching open orders:', err)
    error.value = 'Failed to load open orders'
  }
}

const fetchOrderBook = async () => {
  if (!selectedSymbol.value) return
  
  try {
    const orderBook = await window.GetOrderBook({ 
      symbol: selectedSymbol.value,
      limit: 20
    })
    store.updateOrderBook(selectedSymbol.value, orderBook)
  } catch (err) {
    console.error('Error fetching order book:', err)
    error.value = 'Failed to load order book'
  }
}

const fetchKlines = async () => {
  if (!selectedSymbol.value) return
  
  try {
    const klines = await window.GetKlines({
      symbol: selectedSymbol.value,
      interval: '1h',
      limit: 100
    })
    store.updateKlines(selectedSymbol.value, '1h', klines)
  } catch (err) {
    console.error('Error fetching klines:', err)
    error.value = 'Failed to load price history'
  }
}

// Initial data load
const loadInitialData = async () => {
  try {
    isLoading.value = true
    loadingMessage.value = 'Loading market data...'
    
    // Load available symbols first
    await fetchAvailableSymbols()
    
    // Then load all other data in parallel
    await Promise.all([
      fetchAccountInfo(),
      fetchTickerInfo(),
      fetchOrderBook(),
      fetchKlines(),
      fetchOpenOrders()
    ])
  } catch (err) {
    console.error('Error loading initial data:', err)
    error.value = 'Failed to load trading data. Please try refreshing the page.'
  } finally {
    isLoading.value = false
  }
}

// Lifecycle hooks
onMounted(async () => {
  // Set initial theme from store or system preference
  const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
  isDark.value = store.theme === 'dark' || (store.theme === 'system' && prefersDark)
  document.documentElement.classList.toggle('dark', isDark.value)
  
  // Set up theme change listener
  const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  const handleSystemThemeChange = (e: MediaQueryListEvent) => {
    if (store.theme === 'system') {
      isDark.value = e.matches
      document.documentElement.classList.toggle('dark', e.matches)
    }
  }
  darkModeMediaQuery.addEventListener('change', handleSystemThemeChange)
  
  // Initial data load
  await loadInitialData()
  
  // Set up polling for real-time data
  const tickerInterval = setInterval(fetchTickerInfo, 5000)
  const orderBookInterval = setInterval(fetchOrderBook, 10000)
  const accountUpdateInterval = store.isTestMode ? null : setInterval(fetchAccountInfo, 30000)
  
  // Clean up
  onUnmounted(() => {
    clearInterval(tickerInterval)
    clearInterval(orderBookInterval)
    if (accountUpdateInterval) clearInterval(accountUpdateInterval)
    darkModeMediaQuery.removeEventListener('change', handleSystemThemeChange)
  })
})

// Watch for symbol changes
watch(selectedSymbol, (newSymbol) => {
  if (!newSymbol) return
  
  store.setSymbol(newSymbol)
  Promise.all([
    fetchTickerInfo(),
    fetchOrderBook(),
    fetchKlines(),
    fetchOpenOrders()
  ])
})
</script>

<style scoped>
.trading-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--color-bg);
  color: var(--color-text);
  position: relative;
  overflow: hidden;
}

/* Header */
.trading-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: var(--color-card);
  border-bottom: 1px solid var(--color-border);
  z-index: 10;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  height: 56px;
  flex-shrink: 0;
}

.symbol-selector {
  display: flex;
  align-items: center;
  gap: 1rem;
  min-width: 0;
}

.symbol-selector select {
  background-color: var(--color-bg);
  border: 1px solid var(--color-border);
  color: var(--color-text);
  border-radius: 4px;
  padding: 0.375rem 2rem 0.375rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  min-width: 120px;
  transition: all 0.15s ease;
}

.symbol-selector select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.symbol-selector select:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.price-info {
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.price {
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.25;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.change {
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.test-mode-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  background-color: var(--color-warning/0.1);
  color: var(--color-warning);
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.625rem;
  border-radius: 9999px;
  border: 1px solid var(--color-warning/0.2);
}

.test-mode-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  background-color: currentColor;
  border-radius: 50%;
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 0.375rem;
  color: var(--color-text);
  transition: all 0.15s ease;
}

.btn-icon:hover {
  background-color: var(--color-bg);
}

.btn-icon:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-primary/0.2);
}

.btn-icon:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Main Content */
.trading-main {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 300px;
  grid-template-rows: 1fr auto;
  grid-template-areas:
    "chart orderbook"
    "orderform orderbook";
  gap: 1rem;
  padding: 1rem;
  overflow: hidden;
  min-height: 0;
}

.chart-section {
  grid-area: chart;
  background-color: var(--color-card);
  border-radius: 0.5rem;
  border: 1px solid var(--color-border);
  overflow: hidden;
  position: relative;
}

.order-book-section {
  grid-area: orderbook;
  background-color: var(--color-card);
  border-radius: 0.5rem;
  border: 1px solid var(--color-border);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.order-form-section {
  grid-area: orderform;
  background-color: var(--color-card);
  border-radius: 0.5rem;
  border: 1px solid var(--color-border);
  padding: 1rem;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 50;
  gap: 1rem;
}

.loading-spinner {
  width: 2.5rem;
  height: 2.5rem;
  border: 3px solid var(--color-primary/0.2);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  color: var(--color-text);
  font-size: 0.875rem;
  font-weight: 500;
}

/* Error Message */
.error-message {
  position: fixed;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--color-error);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  z-index: 50;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  animation: slideUp 0.2s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translate(-50%, 1rem);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

/* Responsive */
@media (max-width: 1024px) {
  .trading-main {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr auto auto;
    grid-template-areas:
      "chart"
      "orderform"
      "orderbook";
  }
  
  .order-book-section {
    max-height: 300px;
  }
}

@media (max-width: 640px) {
  .trading-header {
    flex-direction: column;
    height: auto;
    padding: 0.5rem;
    gap: 0.5rem;
  }
  
  .symbol-selector {
    width: 100%;
    justify-content: space-between;
  }
  
  .price-info {
    text-align: right;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
    padding-top: 0.5rem;
    border-top: 1px solid var(--color-border);
  }
  
  .trading-main {
    padding: 0.5rem;
    gap: 0.5rem;
  }
}

/* Dark mode adjustments */
.dark .symbol-selector select {
  background-color: var(--color-bg);
  color: var(--color-text);
}

/* Print styles */
@media print {
  .trading-header,
  .order-form-section,
  .loading-overlay {
    display: none !important;
  }
  
  .trading-main {
    display: block !important;
    padding: 0 !important;
  }
  
  .chart-section,
  .order-book-section {
    border: none !important;
    border-radius: 0 !important;
    page-break-inside: avoid;
  }
}
  border-bottom: 1px solid #334155;
  z-index: 10;
}

.symbol-selector {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.select {
  background-color: #0F172A;
  color: #E2E8F0;
  border: 1px solid #334155;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem 0.5rem 0.75rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  outline: none;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%2394a3b8' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  min-width: 120px;
}

.price-info {
  display: flex;
  flex-direction: column;
}

.price {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.2;
}

.change {
  font-size: 0.75rem;
  line-height: 1;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 0.375rem;
  color: #94A3B8;
  background-color: transparent;
  border: 1px solid #334155;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-icon:hover {
  background-color: #1E293B;
  color: #E2E8F0;
}

.trading-main {
  display: grid;
  grid-template-columns: 1fr 300px;
  grid-template-rows: 1fr auto;
  gap: 1rem;
  flex: 1;
  padding: 1rem;
  overflow: hidden;
}

.chart-section {
  grid-column: 1;
  grid-row: 1 / span 2;
  background-color: #1E293B;
  border-radius: 0.5rem;
  overflow: hidden;
}

.order-book-section {
  grid-column: 2;
  grid-row: 1;
  background-color: #1E293B;
  border-radius: 0.5rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.order-form-section {
  grid-column: 2;
  grid-row: 2;
  background-color: #1E293B;
  border-radius: 0.5rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(15, 23, 42, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 50;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 0.25rem solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top-color: #3B82F6;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .trading-main {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
  }
  
  .chart-section {
    grid-column: 1;
    grid-row: 1;
    height: 500px;
  }
  
  .order-book-section {
    grid-column: 1;
    grid-row: 2;
    height: 300px;
  }
  
  .order-form-section {
    grid-column: 1;
    grid-row: 3;
  }
}

@media (max-width: 640px) {
  .trading-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem;
  }
  
  .symbol-selector {
    width: 100%;
    justify-content: space-between;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .trading-main {
    padding: 0.5rem;
    gap: 0.5rem;
  }
  
  .chart-section {
    height: 400px;
  }
  
  .order-book-section {
    height: 250px;
  }
}
</style>
