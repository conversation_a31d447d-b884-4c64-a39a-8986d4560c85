import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Ref } from 'vue'

interface OrderBookEntry {
  price: string
  quantity: string
}

interface OrderBook {
  symbol: string
  bids: OrderBookEntry[]
  asks: OrderBookEntry[]
  timestamp: number
  lastUpdateId: number
}

interface Kline {
  time: number
  open: string
  high: string
  low: string
  close: string
  volume: string
  isClosed: boolean
}

export const useTradingStore = defineStore('trading', () => {
  // State
  const selectedSymbol = ref('BTCUSDT')
  const orderBook: Ref<OrderBook | null> = ref(null)
  const klines: Ref<Kline[]> = ref([])
  const klineInterval = ref('1m')
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  // Getters
  const midPrice = computed(() => {
    if (!orderBook.value || !orderBook.value.bids.length || !orderBook.value.asks.length) {
      return null
    }
    const bestBid = parseFloat(orderBook.value.bids[0].price)
    const bestAsk = parseFloat(orderBook.value.asks[0].price)
    return ((bestBid + bestAsk) / 2).toFixed(8)
  })

  // Actions
  async function fetchOrderBook() {
    try {
      isLoading.value = true
      error.value = null
      
      // This will be implemented to call the backend
      console.log('Fetching order book for', selectedSymbol.value)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300))
      
      // TODO: Replace with actual API call
      // const response = await window.yourBackendMethod('getOrderBook', { symbol: selectedSymbol.value })
      // orderBook.value = response
      
    } catch (err) {
      console.error('Error fetching order book:', err)
      error.value = 'Failed to fetch order book'
    } finally {
      isLoading.value = false
    }
  }
  
  async function fetchKlines() {
    try {
      isLoading.value = true
      error.value = null
      
      console.log('Fetching klines for', selectedSymbol.value, klineInterval.value)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300))
      
      // TODO: Replace with actual API call
      // const response = await window.yourBackendMethod('getKlines', { 
      //   symbol: selectedSymbol.value,
      //   interval: klineInterval.value
      // })
      // klines.value = response
      
    } catch (err) {
      console.error('Error fetching klines:', err)
      error.value = 'Failed to fetch klines'
    } finally {
      isLoading.value = false
    }
  }
  
  function setSymbol(symbol: string) {
    selectedSymbol.value = symbol
    fetchOrderBook()
    fetchKlines()
  }
  
  function setInterval(interval: string) {
    klineInterval.value = interval
    fetchKlines()
  }

  // Initialize
  fetchOrderBook()
  fetchKlines()

  return {
    // State
    selectedSymbol,
    orderBook,
    klines,
    klineInterval,
    isLoading,
    error,
    
    // Getters
    midPrice,
    
    // Actions
    fetchOrderBook,
    fetchKlines,
    setSymbol,
    setInterval
  }
})
