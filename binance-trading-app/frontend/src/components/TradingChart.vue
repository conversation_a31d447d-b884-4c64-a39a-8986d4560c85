<template>
  <div class="trading-chart-container">
    <div class="chart-header">
      <div class="symbol-selector">
        <select v-model="symbol" @change="onSymbolChange" class="select">
          <option v-for="s in symbols" :key="s" :value="s">{{ s }}</option>
        </select>
      </div>
      <div class="interval-selector">
        <button 
          v-for="interval in intervals" 
          :key="interval"
          @click="onIntervalChange(interval)"
          :class="['interval-btn', { active: interval === store.klineInterval }]"
        >
          {{ interval }}
        </button>
      </div>
    </div>
    <div ref="chartContainer" class="chart"></div>
    <div v-if="store.isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
    </div>
    <div v-if="store.error" class="error-message">
      {{ store.error }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { createChart, ColorType, CrosshairMode } from 'lightweight-charts'
import { useTradingStore } from '@/stores/trading'

const props = defineProps({
  width: {
    type: Number,
    default: 800
  },
  height: {
    type: Number,
    default: 500
  },
  symbols: {
    type: Array<string>,
    default: () => ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT']
  },
  intervals: {
    type: Array<string>,
    default: () => ['1m', '5m', '15m', '1h', '4h', '1d']
  }
})

const store = useTradingStore()
const chartContainer = ref<HTMLElement | null>(null)
const chart = ref<any>(null)
const candleSeries = ref<any>(null)
const volumeSeries = ref<any>(null)
const symbol = ref(store.selectedSymbol)

// Initialize chart
const initChart = () => {
  if (!chartContainer.value) return
  
  // Clean up existing chart if it exists
  if (chart.value) {
    chart.value.remove()
  }
  
  // Create new chart
  chart.value = createChart(chartContainer.value, {
    width: props.width,
    height: props.height,
    layout: {
      background: { type: 'solid', color: '#1E293B' },
      textColor: '#D1D5DB',
    },
    grid: {
      vertLines: {
        color: '#334155',
      },
      horzLines: {
        color: '#334155',
      },
    },
    crosshair: {
      mode: CrosshairMode.Normal,
    },
    rightPriceScale: {
      borderColor: '#334155',
    },
    timeScale: {
      borderColor: '#334155',
      timeVisible: true,
      secondsVisible: false,
    },
  })
  
  // Add candlestick series
  candleSeries.value = chart.value.addCandlestickSeries({
    upColor: '#10B981',
    downColor: '#EF4444',
    borderVisible: false,
    wickUpColor: '#10B981',
    wickDownColor: '#EF4444',
  })
  
  // Add volume series (optional)
  volumeSeries.value = chart.value.addHistogramSeries({
    color: 'rgba(59, 130, 246, 0.5)',
    priceFormat: {
      type: 'volume',
    },
    priceScaleId: '', // set as an overlay by setting a blank priceScaleId
  })
  
  // Set initial data
  updateChart()
  
  // Handle window resize
  const handleResize = () => {
    if (chart.value && chartContainer.value?.parentElement) {
      const { width, height } = chartContainer.value.parentElement.getBoundingClientRect()
      chart.value.applyOptions({ width, height })
    }
  }
  
  window.addEventListener('resize', handleResize)
  return () => {
    window.removeEventListener('resize', handleResize)
    if (chart.value) {
      chart.value.remove()
    }
  }
}

// Update chart with new data
const updateChart = () => {
  if (!candleSeries.value || !volumeSeries.value) return
  
  // Format kline data for the chart
  const candleData = store.klines.map((k: any) => ({
    time: k.time / 1000, // Convert to seconds
    open: parseFloat(k.open),
    high: parseFloat(k.high),
    low: parseFloat(k.low),
    close: parseFloat(k.close),
  }))
  
  // Format volume data
  const volumeData = store.klines.map((k: any) => ({
    time: k.time / 1000, // Convert to seconds
    value: parseFloat(k.volume),
    color: parseFloat(k.close) >= parseFloat(k.open) ? 'rgba(16, 185, 129, 0.5)' : 'rgba(239, 68, 68, 0.5)',
  }))
  
  // Update the chart
  candleSeries.value.setData(candleData)
  volumeSeries.value.setData(volumeData)
  
  // Auto-resize the chart
  chart.value.timeScale().fitContent()
}

// Handle symbol change
const onSymbolChange = () => {
  store.setSymbol(symbol.value)
}

// Handle interval change
const onIntervalChange = (interval: string) => {
  store.setInterval(interval)
}

// Watch for kline updates
watch(() => store.klines, () => {
  updateChart()
}, { deep: true })

// Initialize
onMounted(() => {
  initChart()
})

// Clean up
onUnmounted(() => {
  if (chart.value) {
    chart.value.remove()
  }
})
</script>

<style scoped>
.trading-chart-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #1E293B;
  border-radius: 0.5rem;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: #0F172A;
  border-bottom: 1px solid #334155;
}

.select {
  background-color: #1E293B;
  color: #F1F5F9;
  border: 1px solid #334155;
  border-radius: 0.375rem;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  outline: none;
  cursor: pointer;
}

.interval-selector {
  display: flex;
  gap: 0.25rem;
  background-color: #1E293B;
  border-radius: 0.375rem;
  padding: 0.25rem;
}

.interval-btn {
  background: none;
  border: none;
  color: #94A3B8;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
}

.interval-btn:hover {
  background-color: #334155;
  color: #E2E8F0;
}

.interval-btn.active {
  background-color: #3B82F6;
  color: white;
}

.chart {
  width: 100%;
  height: calc(100% - 3.5rem);
}

.loading-overlay {
  position: absolute;
  top: 3.5rem;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.loading-spinner {
  width: 2.5rem;
  height: 2.5rem;
  border: 0.25rem solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top-color: #3B82F6;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-message {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  background-color: #FEE2E2;
  color: #B91C1C;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  z-index: 20;
}
</style>
