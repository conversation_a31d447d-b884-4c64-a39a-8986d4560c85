<template>
  <div class="order-form">
    <div class="form-header">
      <h3>New Order</h3>
      <div class="account-balance" v-if="accountBalance">
        Available: {{ formatNumber(accountBalance.free) }} {{ baseAsset }}
      </div>
    </div>
    
    <div class="form-section">
      <label class="form-label">Order Type</label>
      <div class="tabs">
        <button 
          v-for="type in orderTypes" 
          :key="type.value"
          @click="orderType = type.value"
          :class="['tab', { 'active': orderType === type.value }]"
        >
          {{ type.label }}
        </button>
      </div>
    </div>
    
    <div class="form-section">
      <label class="form-label">Side</label>
      <div class="side-buttons">
        <button 
          @click="side = 'BUY'" 
          :class="['side-btn', 'buy', { 'active': side === 'BUY' }]"
        >
          Buy / Long
        </button>
        <button 
          @click="side = 'SELL'" 
          :class="['side-btn', 'sell', { 'active': side === 'SELL' }]"
        >
          Sell / Short
        </button>
      </div>
    </div>
    
    <div class="form-section">
      <label class="form-label">Amount ({{ baseAsset }})</label>
      <div class="input-group">
        <input 
          type="number" 
          v-model="amount" 
          :class="['form-input', { 'error': amountError }]"
          placeholder="0.0"
          step="0.00000001"
          min="0"
          @input="onAmountChange"
        >
        <div class="input-actions">
          <button 
            v-for="percent in [25, 50, 75, 100]" 
            :key="percent"
            class="percent-btn"
            @click="setAmountByPercent(percent)"
          >
            {{ percent }}%
          </button>
        </div>
      </div>
      <div v-if="amountError" class="error-message">
        {{ amountError }}
      </div>
    </div>
    
    <div class="form-section">
      <label class="form-label">Price ({{ quoteAsset }})</label>
      <input 
        type="number" 
        v-model="price" 
        :class="['form-input', { 'error': priceError }]"
        placeholder="0.0"
        step="0.00000001"
        min="0"
        :disabled="orderType === 'MARKET'"
      >
      <div v-if="priceError" class="error-message">
        {{ priceError }}
      </div>
    </div>
    
    <div class="form-section" v-if="orderType !== 'MARKET'">
      <div class="flex justify-between items-center mb-1">
        <label class="form-label">Total ({{ quoteAsset }})</label>
        <span class="text-sm text-gray-400">
          {{ formatNumber(calculatedTotal) }}
        </span>
      </div>
      <div class="flex justify-between items-center text-xs text-gray-400">
        <span>Available: {{ formatNumber(quoteBalance) }} {{ quoteAsset }}</span>
        <span>≈ ${{ formatNumber(calculatedTotal) }}</span>
      </div>
    </div>
    
    <button 
      :class="['submit-btn', side.toLowerCase()]"
      :disabled="isSubmitting || !isFormValid"
      @click="submitOrder"
    >
      <span v-if="isSubmitting" class="spinner"></span>
      {{ side }} {{ baseAsset }}
      <span v-if="orderType === 'MARKET'">at Market Price</span>
      <span v-else>@ {{ price }} {{ quoteAsset }}</span>
    </button>
    
    <div v-if="error" class="error-message mt-2">
      {{ error }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useTradingStore } from '@/stores/trading'

const props = defineProps({
  symbol: {
    type: String,
    required: true
  },
  currentPrice: {
    type: String,
    default: '0'
  },
  accountInfo: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['order-submitted'])

const store = useTradingStore()

// Form state
const orderType = ref('LIMIT')
const side = ref('BUY')
const amount = ref('')
const price = ref('')
const isSubmitting = ref(false)
const error = ref('')

// Order types
const orderTypes = [
  { value: 'LIMIT', label: 'Limit' },
  { value: 'MARKET', label: 'Market' },
  { value: 'STOP_LOSS', label: 'Stop Loss' },
  { value: 'TAKE_PROFIT', label: 'Take Profit' }
]

// Computed properties
const baseAsset = computed(() => {
  if (!props.symbol) return ''
  return props.symbol.replace(/USDT$/, '')
})

const quoteAsset = computed(() => 'USDT')

const accountBalance = computed(() => {
  if (!props.accountInfo?.balances) return null
  return props.accountInfo.balances.find((b: any) => b.asset === baseAsset.value)
})

const quoteBalance = computed(() => {
  if (!props.accountInfo?.balances) return 0
  const usdt = props.accountInfo.balances.find((b: any) => b.asset === 'USDT')
  return usdt ? parseFloat(usdt.free) : 0
})

const calculatedTotal = computed(() => {
  if (!amount.value || !price.value) return 0
  return parseFloat(amount.value) * parseFloat(price.value)
})

// Form validation
const amountError = computed(() => {
  if (!amount.value) return 'Amount is required'
  const amountNum = parseFloat(amount.value)
  if (isNaN(amountNum) || amountNum <= 0) return 'Invalid amount'
  
  if (accountBalance.value) {
    const available = parseFloat(accountBalance.value.free)
    if (side.value === 'SELL' && amountNum > available) {
      return `Insufficient ${baseAsset.value} balance`
    }
  }
  
  return ''
})

const priceError = computed(() => {
  if (orderType.value === 'MARKET') return ''
  if (!price.value) return 'Price is required'
  const priceNum = parseFloat(price.value)
  if (isNaN(priceNum) || priceNum <= 0) return 'Invalid price'
  return ''
})

const isFormValid = computed(() => {
  if (orderType.value === 'MARKET') {
    return amount.value && !amountError.value
  }
  return amount.value && price.value && !amountError.value && !priceError.value
})

// Methods
const formatNumber = (num: number) => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 8
  }).format(num)
}

const setAmountByPercent = (percent: number) => {
  if (!accountBalance.value) return
  
  const balance = side.value === 'BUY' 
    ? quoteBalance.value / parseFloat(price.value || props.currentPrice || '1')
    : parseFloat(accountBalance.value.free)
    
  const maxAmount = balance * (percent / 100)
  amount.value = maxAmount.toFixed(8).replace(/\.?0+$/, '')
}

const onAmountChange = () => {
  // Remove any non-numeric characters except decimal point
  amount.value = amount.value.replace(/[^0-9.]/g, '')
  
  // Ensure only one decimal point
  const parts = amount.value.split('.')
  if (parts.length > 2) {
    amount.value = parts[0] + '.' + parts.slice(1).join('')
  }
}

const submitOrder = async () => {
  if (!isFormValid.value) return
  
  try {
    isSubmitting.value = true
    error.value = ''
    
    const order = {
      symbol: props.symbol,
      side: side.value,
      type: orderType.value,
      quantity: amount.value,
      price: orderType.value === 'MARKET' ? undefined : price.value,
      timeInForce: orderType.value === 'LIMIT' ? 'GTC' : undefined
    }
    
    // TODO: Implement API call to submit order
    console.log('Submitting order:', order)
    // const result = await window.yourBackendMethod('createOrder', order)
    
    // Reset form on success
    amount.value = ''
    price.value = ''
    
    // Emit event to parent
    emit('order-submitted')
    
  } catch (err) {
    console.error('Error submitting order:', err)
    error.value = err.message || 'Failed to submit order'
  } finally {
    isSubmitting.value = false
  }
}

// Watch for symbol changes to update price
watch(() => props.currentPrice, (newPrice) => {
  if (newPrice && orderType.value === 'MARKET') {
    price.value = newPrice
  }
}, { immediate: true })

// Watch for order type changes
watch(() => orderType.value, (newType) => {
  if (newType === 'MARKET') {
    price.value = props.currentPrice || '0'
  }
})

// Initialize price with current price
onMounted(() => {
  if (props.currentPrice) {
    price.value = props.currentPrice
  }
})
</script>

<style scoped>
.order-form {
  background-color: #1E293B;
  border-radius: 0.5rem;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 100%;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.form-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  color: #E2E8F0;
}

.account-balance {
  font-size: 0.75rem;
  color: #94A3B8;
}

.form-section {
  margin-bottom: 0.75rem;
}

.form-label {
  display: block;
  font-size: 0.75rem;
  color: #94A3B8;
  margin-bottom: 0.5rem;
}

.tabs {
  display: flex;
  background-color: #0F172A;
  border-radius: 0.375rem;
  padding: 0.25rem;
}

.tab {
  flex: 1;
  padding: 0.375rem 0.5rem;
  font-size: 0.75rem;
  text-align: center;
  border: none;
  background: none;
  color: #94A3B8;
  cursor: pointer;
  border-radius: 0.25rem;
  transition: all 0.2s;
}

.tab:hover {
  color: #E2E8F0;
}

.tab.active {
  background-color: #334155;
  color: white;
  font-weight: 500;
}

.side-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.side-btn {
  padding: 0.5rem;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
}

.side-btn.buy {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10B981;
  border-color: rgba(16, 185, 129, 0.2);
}

.side-btn.buy:hover {
  background-color: rgba(16, 185, 129, 0.2);
}

.side-btn.buy.active {
  background-color: #10B981;
  color: white;
}

.side-btn.sell {
  background-color: rgba(239, 68, 68, 0.1);
  color: #EF4444;
  border-color: rgba(239, 68, 68, 0.2);
}

.side-btn.sell:hover {
  background-color: rgba(239, 68, 68, 0.2);
}

.side-btn.sell.active {
  background-color: #EF4444;
  color: white;
}

.input-group {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  background-color: #0F172A;
  border: 1px solid #334155;
  border-radius: 0.375rem;
  color: #E2E8F0;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #3B82F6;
  box-shadow: 0 0 0 1px #3B82F6;
}

.form-input.error {
  border-color: #EF4444;
}

.input-actions {
  display: flex;
  gap: 0.25rem;
  margin-top: 0.5rem;
}

.percent-btn {
  flex: 1;
  padding: 0.25rem 0.5rem;
  font-size: 0.7rem;
  background-color: #334155;
  border: none;
  border-radius: 0.25rem;
  color: #E2E8F0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.percent-btn:hover {
  background-color: #475569;
}

.submit-btn {
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.2s;
  margin-top: auto;
}

.submit-btn.buy {
  background-color: #10B981;
  color: white;
}

.submit-btn.buy:hover:not(:disabled) {
  background-color: #059669;
}

.submit-btn.sell {
  background-color: #EF4444;
  color: white;
}

.submit-btn.sell:hover:not(:disabled) {
  background-color: #DC2626;
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  display: inline-block;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-message {
  color: #EF4444;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}
</style>
