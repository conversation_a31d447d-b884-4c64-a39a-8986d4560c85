package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"time"

	"binance-trading-app/internal/broker"
	"binance-trading-app/internal/broker/types"
	"binance-trading-app/internal/storage"
)

// App struct
type App struct {
	ctx     context.Context
	broker  broker.Broker
	storage *storage.Storage
}

// NewApp creates a new App application struct
func NewApp() (*App, error) {
	// Initialize storage
	appDataDir, err := getAppDataDir()
	if err != nil {
		return nil, fmt.Errorf("failed to get app data directory: %w", err)
	}

	// In a production app, you'd want to securely generate and store an encryption key
	// For development, we'll use a hardcoded key (NOT RECOMMENDED FOR PRODUCTION)
	storageCfg := storage.DefaultConfig().
		WithDBPath(filepath.Join(appDataDir, "data", "app.db")).
		WithEncryptionKey("dev-key-change-me-in-production")

	storageSvc, err := storage.New(storageCfg)
	if err != nil {
		return nil, fmt.<PERSON><PERSON><PERSON>("failed to initialize storage: %w", err)
	}

	// Initialize the broker in test mode by default
	brokerSvc, err := broker.NewBrokerFactory().CreateBroker(types.BrokerTypeBinance, types.Config{
		TestMode: true,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to initialize broker: %w", err)
	}

	// Connect the broker
	if err := brokerSvc.Connect(context.Background()); err != nil {
		return nil, fmt.Errorf("failed to connect to broker: %w", err)
	}

	return &App{
		broker:  brokerSvc,
		storage: storageSvc,
	}, nil
}

// getAppDataDir returns the appropriate application data directory for the current OS
func getAppDataDir() (string, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", fmt.Errorf("failed to get user home directory: %w", err)
	}

	var appDataDir string
	switch {
	case runtime.GOOS == "windows":
		appDataDir = filepath.Join(homeDir, "AppData", "Local", "BinanceTradingApp")
	case runtime.GOOS == "darwin":
		appDataDir = filepath.Join(homeDir, "Library", "Application Support", "BinanceTradingApp")
	default: // Linux/Unix
		appDataDir = filepath.Join(homeDir, ".local", "share", "binance-trading-app")
	}

	// Create the directory if it doesn't exist
	if err := os.MkdirAll(appDataDir, 0700); err != nil {
		return "", fmt.Errorf("failed to create app data directory: %w", err)
	}

	return appDataDir, nil
}

// domReady is called when the frontend's DOM is ready
func (a *App) domReady(ctx context.Context) {
	// You can add any frontend initialization code here
	log.Println("Frontend DOM is ready")
}

// startup is called when the app starts. The context is saved
// so we can call the runtime methods
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx

	// Log the storage location for debugging
	storagePath := ""
	if a.storage != nil {
		// Get the DB path through a method if available, or log a message
		// that we can't access the path directly due to unexported fields
		storagePath = "[storage path not accessible]"
	}
	log.Printf("Application started. Storage path: %s", storagePath)
}

// SaveAPIKey saves the Binance API key to secure storage
func (a *App) SaveAPIKey(name, apiKey, apiSecret string, isTestnet bool) error {
	binanceStore := storage.NewBinanceStore(a.storage)
	return binanceStore.SaveAPIKey(a.ctx, name, apiKey, apiSecret, isTestnet)
}

// GetAPIKey retrieves a saved Binance API key
func (a *App) GetAPIKey(name string) (map[string]interface{}, error) {
	key, err := a.storage.GetAPIKey(a.ctx, "binance", name)
	if err != nil {
		return nil, err
	}

	// Don't return the actual secret for security
	return map[string]interface{}{
		"name":      key.Name,
		"apiKey":    key.APIKey,
		"isTestnet": key.IsTestnet,
		"createdAt": key.CreatedAt,
		"updatedAt": key.UpdatedAt,
	}, nil
}

// ListAPIKeys returns a list of all saved API keys
func (a *App) ListAPIKeys() ([]map[string]interface{}, error) {
	keys, err := a.storage.ListAPIKeys(a.ctx, "binance")
	if err != nil {
		return nil, err
	}

	result := make([]map[string]interface{}, 0, len(keys))
	for _, key := range keys {
		result = append(result, map[string]interface{}{
			"name":      key.Name,
			"isTestnet": key.IsTestnet,
			"createdAt": key.CreatedAt,
			"updatedAt": key.UpdatedAt,
		})
	}

	return result, nil
}

// DeleteAPIKey removes a saved API key
func (a *App) DeleteAPIKey(name string) error {
	return a.storage.DeleteAPIKey(a.ctx, "binance", name)
}

// SetActiveAPIKey sets the active API key for the Binance client
func (a *App) SetActiveAPIKey(name string) error {
	key, err := a.storage.GetAPIKey(a.ctx, "binance", name)
	if err != nil {
		return fmt.Errorf("failed to get API key: %w", err)
	}

	// Set the active API key in the Binance service
	return a.broker.SetAPIKey(key.APIKey, key.APISecret, key.IsTestnet)
}

// SetBinanceAPIKeys sets the Binance API keys. If either key is empty, switches to test mode.
func (a *App) SetBinanceAPIKeys(apiKey, apiSecret string) error {
	// If API key or secret is empty, use test mode
	testMode := apiKey == "" || apiSecret == ""

	// Update the broker with new API keys and testnet mode
	if err := a.broker.SetAPIKey(apiKey, apiSecret, testMode); err != nil {
		return fmt.Errorf("failed to update broker API keys: %w", err)
	}

	// Create a binance store instance
	binanceStore := storage.NewBinanceStore(a.storage)

	// Save API keys to storage using the binance store
	if err := binanceStore.SaveAPIKey(context.Background(), "default", apiKey, apiSecret, testMode); err != nil {
		return fmt.Errorf("failed to save API keys: %w", err)
	}

	// Test the connection
	_, err := a.broker.GetAccountInfo(context.Background())
	if err != nil {
		return fmt.Errorf("failed to verify API keys: %v", err)
	}

	log.Println("Successfully connected to Binance API")
	return nil
}

// GetAccountInfo returns the account information from the broker
func (a *App) GetAccountInfo() (map[string]interface{}, error) {
	if a.broker == nil {
		return nil, fmt.Errorf("broker not initialized")
	}

	balances, err := a.broker.GetAccountInfo(context.Background())
	if err != nil {
		return nil, fmt.Errorf("failed to get account info: %w", err)
	}

	// Prepare the result with default values
	result := map[string]interface{}{
		"makerCommission": 0, // These fields are not available in the new interface
		"takerCommission": 0,
		"canTrade":        true,
		"canWithdraw":     true,
		"canDeposit":      true,
		"updateTime":      time.Now().Unix() * 1000, // Current time in milliseconds
		"balances":        balances,
	}

	// Add the balances as a map for easier access in the frontend
	balancesMap := make(map[string]map[string]float64)
	for _, balance := range balances {
		balancesMap[balance.Asset] = map[string]float64{
			"free":   balance.Free,
			"locked": balance.Locked,
		}
	}
	result["balancesMap"] = balancesMap

	return result, nil
}

// GetBalance returns the balance for a specific asset
func (a *App) GetBalance(asset string) (map[string]interface{}, error) {
	if a.broker == nil {
		return nil, fmt.Errorf("broker not initialized")
	}

	balance, err := a.broker.GetBalance(context.Background(), asset)
	if err != nil {
		return nil, fmt.Errorf("failed to get balance: %w", err)
	}

	// Convert the balance to a map for the frontend
	result := map[string]interface{}{
		"asset":  balance.Asset,
		"free":   balance.Free,
		"locked": balance.Locked,
	}

	return result, nil
}

// GetTicker returns ticker information for a symbol
func (a *App) GetTicker(symbol string) (map[string]interface{}, error) {
	if a.broker == nil {
		return nil, fmt.Errorf("broker not initialized")
	}

	// For now, return mock data - this should be implemented in the broker interface
	// TODO: Add GetTicker method to broker interface
	return map[string]interface{}{
		"symbol":             symbol,
		"lastPrice":          "50000.00",
		"priceChange":        "1000.00",
		"priceChangePercent": "2.04",
		"volume":             "1234.56",
		"count":              1000,
	}, nil
}

// GetAvailableSymbols returns list of available trading symbols
func (a *App) GetAvailableSymbols() ([]string, error) {
	if a.broker == nil {
		return nil, fmt.Errorf("broker not initialized")
	}

	exchangeInfo, err := a.broker.GetExchangeInfo(context.Background())
	if err != nil {
		return nil, fmt.Errorf("failed to get exchange info: %w", err)
	}

	symbols := make([]string, 0, len(exchangeInfo))
	for _, symbol := range exchangeInfo {
		symbols = append(symbols, symbol.ID)
	}

	return symbols, nil
}

// GetKlines returns kline/candlestick data for a symbol
func (a *App) GetKlines(symbol, interval string, limit int) ([]map[string]interface{}, error) {
	if a.broker == nil {
		return nil, fmt.Errorf("broker not initialized")
	}

	// Subscribe to candles and get recent data
	candlesChan, err := a.broker.SubscribeToCandles(symbol, interval)
	if err != nil {
		return nil, fmt.Errorf("failed to subscribe to candles: %w", err)
	}

	// Get initial data (this is a simplified implementation)
	// In a real implementation, you'd want to get historical data first
	select {
	case candles := <-candlesChan:
		result := make([]map[string]interface{}, 0, len(candles))
		for _, candle := range candles {
			result = append(result, map[string]interface{}{
				"openTime":  candle.OpenTime,
				"closeTime": candle.CloseTime,
				"open":      fmt.Sprintf("%.8f", candle.Open),
				"high":      fmt.Sprintf("%.8f", candle.High),
				"low":       fmt.Sprintf("%.8f", candle.Low),
				"close":     fmt.Sprintf("%.8f", candle.Close),
				"volume":    fmt.Sprintf("%.8f", candle.Volume),
			})
		}
		return result, nil
	case <-time.After(5 * time.Second):
		return nil, fmt.Errorf("timeout waiting for candle data")
	}
}

// GetOrderBook returns order book data for a symbol
func (a *App) GetOrderBook(symbol string, limit int) (map[string]interface{}, error) {
	if a.broker == nil {
		return nil, fmt.Errorf("broker not initialized")
	}

	orderBookChan, err := a.broker.SubscribeToOrderBook(symbol)
	if err != nil {
		return nil, fmt.Errorf("failed to subscribe to order book: %w", err)
	}

	// Get current order book data
	select {
	case orderBook := <-orderBookChan:
		return map[string]interface{}{
			"symbol":       symbol,
			"bids":         orderBook.Bids,
			"asks":         orderBook.Asks,
			"lastUpdateId": orderBook.LastUpdateID,
		}, nil
	case <-time.After(5 * time.Second):
		return nil, fmt.Errorf("timeout waiting for order book data")
	}
}

// CreateOrder creates a new trading order
func (a *App) CreateOrder(symbol, side, orderType string, quantity, price float64) (map[string]interface{}, error) {
	if a.broker == nil {
		return nil, fmt.Errorf("broker not initialized")
	}

	order, err := a.broker.CreateOrder(context.Background(), symbol, side, orderType, quantity, price)
	if err != nil {
		return nil, fmt.Errorf("failed to create order: %w", err)
	}

	return map[string]interface{}{
		"orderId":     order.ID,
		"symbol":      order.Symbol,
		"side":        order.Side,
		"type":        order.Type,
		"quantity":    order.Quantity,
		"price":       order.Price,
		"status":      order.Status,
		"timeInForce": order.TimeInForce,
		"executedQty": order.ExecutedQty,
		"createdAt":   order.CreatedAt,
	}, nil
}

// GetOpenOrders returns open orders for a symbol
func (a *App) GetOpenOrders(symbol string) ([]map[string]interface{}, error) {
	if a.broker == nil {
		return nil, fmt.Errorf("broker not initialized")
	}

	orders, err := a.broker.GetOpenOrders(context.Background(), symbol)
	if err != nil {
		return nil, fmt.Errorf("failed to get open orders: %w", err)
	}

	result := make([]map[string]interface{}, 0, len(orders))
	for _, order := range orders {
		result = append(result, map[string]interface{}{
			"orderId":     order.ID,
			"symbol":      order.Symbol,
			"side":        order.Side,
			"type":        order.Type,
			"quantity":    order.Quantity,
			"price":       order.Price,
			"status":      order.Status,
			"timeInForce": order.TimeInForce,
			"executedQty": order.ExecutedQty,
			"createdAt":   order.CreatedAt,
		})
	}

	return result, nil
}

// CancelOrder cancels an existing order
func (a *App) CancelOrder(orderID, symbol string) error {
	if a.broker == nil {
		return fmt.Errorf("broker not initialized")
	}

	return a.broker.CancelOrder(context.Background(), orderID, symbol)
}

// shutdown is called when the application is shutting down
func (a *App) shutdown(ctx context.Context) {
	// Clean up any resources here
	log.Println("Shutting down application...")

	// Close the broker
	if a.broker != nil {
		// Add any necessary cleanup for the broker here
		// Currently, Broker doesn't require explicit cleanup
	}

	// Close the storage
	if a.storage != nil {
		if err := a.storage.Close(); err != nil {
			log.Printf("Error closing storage: %v", err)
		} else {
			log.Println("Storage closed successfully")
		}
	}
}
