# План разработки Binance Trading App

## 📌 Текущий статус
- [x] Инициализация проекта
- [x] Настройка базовой структуры фронтенда (Vue 3 + TypeScript)
- [x] Настройка бэкенда (Go + Wails)
- [x] Настройка роутинга
- [x] Реализация подключения к Binance API
- [x] Базовая реализация торгового функционала
- [x] Поддержка Binance Spot API
- [x] Поддержка Binance Futures API
- [x] Единый интерфейс для Spot и Futures API
- [x] Базовое тестирование
- [x] Базовая документация
- [x] Тестовый режим (Demo Mode) с виртуальным балансом
- [x] Система безопасного хранения API ключей
- [ ] Интеграция фронтенда с бэкендом
- [ ] Завершение UI компонентов
- [ ] Разработка интерфейса управления аккаунтом
- [ ] Разработка аналитики
- [ ] Тестирование кросс-функциональности

## 🚀 Приоритетные задачи

### Критический приоритет (Блокирующие)
1. **Интеграция фронтенда с бэкендом**
   - Добавить недостающие методы в app.go
   - Исправить вызовы API в TradingView.vue
   - Настроить корректную передачу данных

2. **Очистка и рефакторинг кода**
   - Удалить дублированный код в App.vue
   - Устранить дублирование TestBroker
   - Обновить версию Go до 1.24

### Высокий приоритет
3. **Завершение UI компонентов**
   - Доработать TradingChart с lightweight-charts
   - Реализовать real-time OrderBook
   - Завершить функционал OrderForm

4. **Улучшение пользовательского опыта**
   - Добавить индикаторы загрузки
   - Улучшить обработку ошибок
   - Реализовать уведомления

### Средний приоритет
5. **Расширение функционала**
   - Добавить больше торговых пар
   - Реализовать историю сделок
   - Добавить настройки приложения

## � Детальный план реализации

### Этап 1: Критические исправления (1-2 дня)

#### 1.1 Интеграция фронтенда с бэкендом
**Файлы для изменения:**
- `app.go` - добавить недостающие методы
- `frontend/src/views/TradingView.vue` - исправить вызовы API

**Необходимые методы в app.go:**
```go
// Рыночные данные
func (a *App) GetTicker(symbol string) (map[string]interface{}, error)
func (a *App) GetAvailableSymbols() ([]string, error)
func (a *App) GetKlines(symbol, interval string, limit int) ([]map[string]interface{}, error)
func (a *App) GetOrderBook(symbol string, limit int) (map[string]interface{}, error)

// Торговые операции
func (a *App) CreateOrder(symbol, side, orderType string, quantity, price float64) (map[string]interface{}, error)
func (a *App) GetOpenOrders(symbol string) ([]map[string]interface{}, error)
func (a *App) CancelOrder(orderID, symbol string) error
```

#### 1.2 Очистка кода
- Удалить строки 436-692 в `frontend/src/App.vue`
- Выбрать один TestBroker (рекомендуется `internal/broker/test/broker.go`)
- Удалить `internal/binance/test_broker.go`
- Обновить `go.mod`: изменить `go 1.23.0` на `go 1.24.0`

### Этап 2: Завершение UI компонентов (2-3 дня)

#### 2.1 TradingChart компонент
**Файл:** `frontend/src/components/TradingChart.vue`
- Интегрировать lightweight-charts библиотеку
- Реализовать отображение свечных данных
- Добавить индикаторы объема
- Настроить real-time обновления

#### 2.2 OrderBook компонент
**Файл:** `frontend/src/components/OrderBook.vue`
- Реализовать отображение стакана заявок
- Добавить цветовую индикацию (зеленый/красный)
- Настроить автообновление данных
- Добавить анимации изменений

#### 2.3 OrderForm компонент
**Файл:** `frontend/src/components/OrderForm.vue`
- Реализовать формы для лимитных и рыночных ордеров
- Добавить валидацию полей
- Интегрировать с методами создания ордеров
- Добавить расчет комиссий

### Этап 3: Улучшение UX (1-2 дня)

#### 3.1 Система уведомлений
- Расширить глобальную систему уведомлений
- Добавить уведомления об исполнении ордеров
- Реализовать toast-уведомления для ошибок

#### 3.2 Индикаторы состояния
- Добавить индикаторы загрузки для всех операций
- Реализовать skeleton loading для компонентов
- Добавить индикатор подключения к API

#### 3.3 Обработка ошибок
- Улучшить отображение ошибок API
- Добавить retry механизмы
- Реализовать graceful degradation

### Этап 4: Расширенный функционал (3-5 дней)

#### 4.1 Управление аккаунтом
**Новые файлы:**
- `frontend/src/views/AccountView.vue`
- `frontend/src/components/BalanceOverview.vue`
- `frontend/src/components/OrderHistory.vue`

**Функционал:**
- Детальный просмотр балансов всех активов
- История всех сделок с фильтрацией
- Статистика торговли (P&L, объемы)
- Экспорт данных в CSV/Excel

#### 4.2 Настройки приложения
**Новые файлы:**
- `frontend/src/views/SettingsView.vue`
- `frontend/src/components/APIKeyManager.vue`
- `frontend/src/components/ThemeSelector.vue`

**Функционал:**
- Управление несколькими API ключами
- Настройки темы (светлая/темная/авто)
- Настройки уведомлений
- Языковые настройки

#### 4.3 Аналитика и отчеты
**Новые файлы:**
- `frontend/src/views/AnalyticsView.vue`
- `frontend/src/components/ProfitLossChart.vue`
- `frontend/src/components/TradeStatistics.vue`

**Функционал:**
- Графики прибыли/убытков
- Анализ эффективности торговли
- Сравнение с рынком
- Детальная статистика по парам

## 🔧 Технические требования

### Версии и зависимости
- **Go**: 1.24+
- **Node.js**: 18+
- **Wails**: v2.10.1+
- **Vue**: 3.2+
- **TypeScript**: 4.6+

### Архитектурные принципы
- Разделение ответственности между слоями
- Единый интерфейс для всех типов брокеров
- Безопасное хранение чувствительных данных
- Graceful degradation при ошибках API
- Responsive design для разных размеров экрана

### Безопасность
- Шифрование API ключей в локальном хранилище
- Валидация всех пользовательских вводов
- Защита от XSS и injection атак
- Безопасная передача данных между фронтендом и бэкендом

## 📋 Чек-лист для каждого этапа

### Этап 1 - Готовность к проверке:
- [ ] Все методы API добавлены в app.go
- [ ] TradingView.vue корректно вызывает методы бэкенда
- [ ] Дублированный код удален
- [ ] Версия Go обновлена
- [ ] Приложение запускается без ошибок
- [ ] Тестовый режим работает корректно

### Этап 2 - Готовность к проверке:
- [ ] TradingChart отображает свечи
- [ ] OrderBook показывает актуальные данные
- [ ] OrderForm создает ордера
- [ ] Real-time обновления работают
- [ ] UI responsive на разных размерах экрана

### Этап 3 - Готовность к проверке:
- [ ] Уведомления работают для всех операций
- [ ] Индикаторы загрузки везде присутствуют
- [ ] Ошибки обрабатываются gracefully
- [ ] UX интуитивно понятен

### Этап 4 - Готовность к проверке:
- [ ] Все новые view реализованы
- [ ] Навигация между разделами работает
- [ ] Данные корректно отображаются
- [ ] Экспорт данных функционирует

## 🚨 Критические моменты

### Безопасность API ключей
- **НИКОГДА** не логировать полные API ключи
- Использовать только зашифрованное хранение
- Проверять права доступа перед каждым API вызовом

### Обработка ошибок сети
- Всегда предусматривать таймауты
- Реализовать exponential backoff для retry
- Показывать понятные сообщения пользователю

### Производительность
- Ограничивать частоту API запросов
- Кэшировать статические данные
- Использовать WebSocket для real-time данных

## 📅 История изменений

### [План обновлен] 2025-06-16
- Проведен полный анализ текущего состояния проекта
- Выявлены критические проблемы интеграции фронтенда с бэкендом
- Составлен детальный поэтапный план реализации
- Добавлены чек-листы для контроля качества
- Определены технические требования и принципы безопасности
- Расставлены приоритеты задач по критичности

### [Завершено] 2025-06-16
- Реализована поддержка как Spot, так и Futures API Binance
- Создана унифицированная прослойка для работы с обоими типами API
- Реализованы адаптеры для конвертации данных между Spot и Futures API
- Добавлена поддержка различных типов ордеров и их статусов
- Оптимизирована работа с балансом для обоих типов счетов
- Улучшена обработка ошибок и логирование
- Реализован полнофункциональный тестовый режим с виртуальным балансом
- Добавлена система безопасного хранения API ключей с шифрованием
- Написаны и протестированы unit тесты для тестового брокера
- Создана базовая структура фронтенда с Vue 3 + TypeScript

### [Завершено] 2025-06-15
- Инициализирован проект с Wails v2
- Настроена базовая конфигурация Go + Vue
- Создана правильная структура каталогов
- Настроена интеграция между Go и Vue через Wails
- Добавлены основные зависимости (go-binance, SQLite, etc.)

## 🔧 Технические детали

### 📱 Режим тестирования (Demo Mode)

#### Активация
- Режим активируется автоматически при инициализации брокера без API ключей
- Все операции выполняются локально без реальных запросов к бирже
- Идеально для тестирования стратегий и знакомства с интерфейсом

#### Особенности работы

##### Баланс и средства
- Генерируется начальный виртуальный баланс (например, 10,000 USDT)
- Поддерживаются все основные валюты с реалистичными начальными значениями
- Изменения баланса происходят только при исполнении тестовых ордеров

##### Работа с ордерами

1. **Создание ордера**
   - Ордер сохраняется в локальное хранилище
   - Записывается время создания и последней проверки
   - Присваивается статус `NEW`

2. **Проверка исполнения**
   - При поступлении новых свечных данных проверяются все активные ордера
   - Для каждого ордера анализируется диапазон цен свечи (high/low)
   - Если цена ордера попадает в диапазон свечи, ордер считается исполненным
   - Время исполнения фиксируется как время закрытия свечи

3. **Исполнение ордера**
   - При срабатывании ордера:
     - Обновляется виртуальный баланс
     - Генерируется сделка с ценой исполнения
     - Ордер переводится в статус `FILLED`
     - В историю добавляется запись о сделке

##### Рыночные данные
- Используются реальные рыночные данные через публичные API
- Поддерживается подписка на стакан и свечи
- Все данные кэшируются локально для оффлайн-работы

##### Ограничения
- Невозможно вывести средства
- Нет доступа к реальным средствам
- Некоторые функции, требующие приватных ключей, недоступны

#### Преимущества
- Безопасное тестирование стратегий
- Мгновенное исполнение ордеров
- Реалистичная симуляция торговли
- Сохранение истории операций

#### Переход на реальный счет
1. Пользователь добавляет API ключи в настройках
2- Приложение переключается в рабочий режим
3. Все новые операции выполняются с реальным счетом
4. История тестовых операций сохраняется отдельно

## 🔧 Технические детали

### 🛠 Реализованный функционал
- Абстрактный интерфейс брокера (`Broker`)
- Унифицированные адаптеры для Binance Spot и Futures API
- Поддержка REST и WebSocket API для обоих типов счетов
- Полный цикл работы с ордерами (создание, отмена, получение статуса)
- Подписка на рыночные данные (свечи, стакан)
- Управление балансом и позициями
- Конвертация типов и статусов между Spot и Futures API
- Единый интерфейс для работы с обоими типами счетов

### Используемые технологии
- Бэкенд: Go 1.24
- Фронтенд: Vue 3, TypeScript
- UI: Компонентный подход
- API: 
  - Binance Spot API
  - Binance Futures API
- WebSockets: Для получения рыночных данных в реальном времени
- go-binance/v2: Официальная библиотека для работы с Binance API
- Wails: Для интеграции Go и Vue
