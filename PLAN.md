# План разработки Binance Trading App

## 📌 Текущий статус
- [x] Инициализация проекта
- [x] Настройка базовой структуры фронтенда (Vue 3 + TypeScript)
- [x] Настройка бэкенда (Go + Wails)
- [x] Настройка роутинга
- [x] Реализация подключения к Binance API
- [x] Базовая реализация торгового функционала
- [ ] Разработка интерфейса управления аккаунтом
- [ ] Разработка аналитики
- [x] Базовое тестирование
- [x] Базовая документация
- [x] Поддержка Binance Spot API
- [x] Поддержка Binance Futures API
- [x] Единый интерфейс для Spot и Futures API
- [ ] Тестирование кросс-функциональности

## 🚀 Ближайшие задачи
1. Завершить тестирование кросс-функциональности Spot/Futures
2. Исправить ошибки в маппинге типов между Spot и Futures API
3. Оптимизировать обработку ошибок и логирование
4. Обновить документацию по работе с обоими типами API

## 📅 История изменений

### [В процессе] 2025-06-16
- Реализована поддержка как Spot, так и Futures API Binance
- Создана унифицированная прослойка для работы с обоими типами API
- Реализованы адаптеры для конвертации данных между Spot и Futures API
- Добавлена поддержка различных типов ордеров и их статусов
- Оптимизирована работа с балансом для обоих типов счетов
- Улучшена обработка ошибок и логирование

### [Завершено] 2025-06-16
- Реализовано базовое подключение к Binance API
- Создан абстрактный слой брокера с поддержкой нескольких бирж
- Реализован адаптер для Binance Futures API
- Добавлена поддержка WebSocket для получения рыночных данных
- Реализованы методы для работы с ордерами и балансом
- Настроена обработка ошибок и логирование

### [Завершено] 2025-06-15
- Инициализирован проект
- Настроена базовая конфигурация
- Создана структура каталогов
- Настроена интеграция между Go и Vue через Wails

## 🔧 Технические детали

### 📱 Режим тестирования (Demo Mode)

#### Активация
- Режим активируется автоматически при инициализации брокера без API ключей
- Все операции выполняются локально без реальных запросов к бирже
- Идеально для тестирования стратегий и знакомства с интерфейсом

#### Особенности работы

##### Баланс и средства
- Генерируется начальный виртуальный баланс (например, 10,000 USDT)
- Поддерживаются все основные валюты с реалистичными начальными значениями
- Изменения баланса происходят только при исполнении тестовых ордеров

##### Работа с ордерами

1. **Создание ордера**
   - Ордер сохраняется в локальное хранилище
   - Записывается время создания и последней проверки
   - Присваивается статус `NEW`

2. **Проверка исполнения**
   - При поступлении новых свечных данных проверяются все активные ордера
   - Для каждого ордера анализируется диапазон цен свечи (high/low)
   - Если цена ордера попадает в диапазон свечи, ордер считается исполненным
   - Время исполнения фиксируется как время закрытия свечи

3. **Исполнение ордера**
   - При срабатывании ордера:
     - Обновляется виртуальный баланс
     - Генерируется сделка с ценой исполнения
     - Ордер переводится в статус `FILLED`
     - В историю добавляется запись о сделке

##### Рыночные данные
- Используются реальные рыночные данные через публичные API
- Поддерживается подписка на стакан и свечи
- Все данные кэшируются локально для оффлайн-работы

##### Ограничения
- Невозможно вывести средства
- Нет доступа к реальным средствам
- Некоторые функции, требующие приватных ключей, недоступны

#### Преимущества
- Безопасное тестирование стратегий
- Мгновенное исполнение ордеров
- Реалистичная симуляция торговли
- Сохранение истории операций

#### Переход на реальный счет
1. Пользователь добавляет API ключи в настройках
2- Приложение переключается в рабочий режим
3. Все новые операции выполняются с реальным счетом
4. История тестовых операций сохраняется отдельно

## 🔧 Технические детали

### 🛠 Реализованный функционал
- Абстрактный интерфейс брокера (`Broker`)
- Унифицированные адаптеры для Binance Spot и Futures API
- Поддержка REST и WebSocket API для обоих типов счетов
- Полный цикл работы с ордерами (создание, отмена, получение статуса)
- Подписка на рыночные данные (свечи, стакан)
- Управление балансом и позициями
- Конвертация типов и статусов между Spot и Futures API
- Единый интерфейс для работы с обоими типами счетов

### Используемые технологии
- Бэкенд: Go 1.24
- Фронтенд: Vue 3, TypeScript
- UI: Компонентный подход
- API: 
  - Binance Spot API
  - Binance Futures API
- WebSockets: Для получения рыночных данных в реальном времени
- go-binance/v2: Официальная библиотека для работы с Binance API
- Wails: Для интеграции Go и Vue
